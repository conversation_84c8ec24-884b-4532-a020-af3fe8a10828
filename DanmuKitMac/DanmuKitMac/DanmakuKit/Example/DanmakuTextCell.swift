//
//  DanmakuTextCell.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Example text danmaku cell
//

import Cocoa

public class DanmakuTextCell: DanmakuCell {
    
    public override func willDisplay() {
        super.willDisplay()
    }
    
    public override func displaying(_ context: CGContext, _ size: CGSize, _ isCancelled: Bool) {
        guard !isCancelled, let model = model as? DanmakuTextCellModel else { return }
        // 可配置的描边+阴影（默认都为 0，相当于纯文字，与 iOS 视觉一致）
        NSGraphicsContext.saveGraphicsState()
        let nsContext = NSGraphicsContext(cgContext: context, flipped: false)
        NSGraphicsContext.current = nsContext
        defer { NSGraphicsContext.restoreGraphicsState() }

        let text = NSString(string: model.text)

        let attributes: [NSAttributedString.Key: Any] = [
            .font: model.font,
            .foregroundColor: model.textColor
        ]

        // 先描边
        if model.strokeWidth > 0 || model.strokeOpacity > 0 {
            context.setLineWidth(max(0, model.strokeWidth))
            context.setLineJoin(.round)
            let strokeColor = model.strokeColor.withAlphaComponent(model.strokeOpacity)
            context.setStrokeColor(strokeColor.cgColor)
            context.setTextDrawingMode(.stroke)
            text.draw(at: .zero, withAttributes: attributes)
        }

        // 再填充 + 阴影（如果需要）
        context.saveGState()
        if model.shadowOpacity > 0 && (model.shadowBlur > 0 || model.shadowOffset != .zero) {
            let shadowColor = model.shadowColor.withAlphaComponent(model.shadowOpacity)
            context.setShadow(offset: model.shadowOffset, blur: model.shadowBlur, color: shadowColor.cgColor)
        }
        context.setTextDrawingMode(.fill)
        text.draw(at: .zero, withAttributes: attributes)
        context.restoreGState()

        // 调试可视化：画三条竖线，便于确认文本右边界与 cell 右边界
        if let dv = superview as? DanmakuView, dv.debugTrackFloating {
            // 计算文本渲染宽度（按同一属性测量）
            let at = NSAttributedString(string: model.text, attributes: attributes)
            let textRect = at.boundingRect(with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude), options: [.usesLineFragmentOrigin, .usesFontLeading])
            let textW = ceil(textRect.width)

            context.saveGState()
            // 0: 左边界（黄）
            context.setStrokeColor(NSColor.systemYellow.cgColor)
            context.setLineWidth(1)
            context.move(to: CGPoint(x: 0, y: 0))
            context.addLine(to: CGPoint(x: 0, y: bounds.height))
            context.strokePath()

            // 文本右边界（绿）
            context.setStrokeColor(NSColor.systemGreen.cgColor)
            context.move(to: CGPoint(x: textW, y: 0))
            context.addLine(to: CGPoint(x: textW, y: bounds.height))
            context.strokePath()

            // cell 右边界（红）
            context.setStrokeColor(NSColor.systemRed.cgColor)
            context.move(to: CGPoint(x: bounds.width, y: 0))
            context.addLine(to: CGPoint(x: bounds.width, y: bounds.height))
            context.strokePath()
            context.restoreGState()

            print("[DanmakuDebug] draw cellW=\(Int(bounds.width)) textW=\(Int(textW))")
        }
    }
    
    public override func didDisplay(_ finished: Bool) {
        super.didDisplay(finished)
    }
    
}
