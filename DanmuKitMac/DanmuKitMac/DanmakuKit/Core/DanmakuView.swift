//
//  DanmakuView.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

public protocol DanmakuViewDelegate: AnyObject {

    /// 将要复用弹幕 Cell（在调用前已为你设置好对应的模型）
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 将要复用的弹幕 Cell
    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell)

    /// 没有空间可以发射弹幕（不可重叠且所有轨道均无法容纳时触发）
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 尝试发射的弹幕模型
    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel)

    /// 即将显示弹幕（进入轨道前回调）
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 将要显示的弹幕 Cell
    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell)

    /// 弹幕显示结束（离开轨道后回调）
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 结束显示的弹幕 Cell
    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell)

    /// 点击了弹幕
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 被点击的弹幕 Cell
    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell)

    /// 轻点弹幕（与 iOS 命名对齐的回调）
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 被轻点的弹幕 Cell
    func danmakuView(_ danmakuView: DanmakuView, didTapped danmaku: DanmakuCell)

    /// 没有空间可以同步显示弹幕（sync 时触发）
    /// - Parameters:
    ///   - danmakuView: 弹幕视图
    ///   - danmaku: 尝试同步显示的弹幕模型
    func danmakuView(_ danmakuView: DanmakuView, noSpaceSync danmaku: DanmakuCellModel)

}

public extension DanmakuViewDelegate {

    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel) {}

    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, didTapped danmaku: DanmakuCell) {}

    func danmakuView(_ danmakuView: DanmakuView, noSpaceSync danmaku: DanmakuCellModel) {}

}

public enum DanmakuStatus {
    case play
    case pause
    case stop
}

public class DanmakuView: NSView {

    public weak var delegate: DanmakuViewDelegate?

    /// 是否开启弹幕 Cell 复用（关闭后不再调用 delegate 的复用回调）
    public var enableCellReusable = false

    /// 轨道高度（每条弹幕所在的轨道高度，影响可用轨道数量）
    public var trackHeight: CGFloat = 20 {
        didSet {
            guard oldValue != trackHeight else { return }
            recalculateTracks()
        }
    }

    /// 顶部内边距（影响顶部弹幕的实际偏移）
    public var paddingTop: CGFloat = 0 {
        didSet {
            guard oldValue != paddingTop else { return }
            recalculateTracks()
        }
    }

    /// 底部内边距（影响底部弹幕的实际偏移）
    public var paddingBottom: CGFloat = 0 {
        didSet {
            guard oldValue != paddingBottom else { return }
            recalculateTracks()
        }
    }

    /// 是否启用“滚动弹幕”（漂浮弹幕）
    public var enableFloatingDanmaku = true {
        didSet { if !enableFloatingDanmaku { floatingTracks.forEach { $0.stop() } } }
    }

    /// 是否启用“顶部固定弹幕”
    public var enableTopDanmaku = true {
        didSet { if !enableTopDanmaku { topTracks.forEach { $0.stop() } } }
    }

    /// 是否启用“底部固定弹幕”
    public var enableBottomDanmaku = true {
        didSet { if !enableBottomDanmaku { bottomTracks.forEach { $0.stop() } } }
    }

    /// 显示区域占比 [0, 1]（与 iOS 行为一致，影响可分配轨道的数量与垂直居中）
    public var displayArea: CGFloat = 1.0 {
        willSet { assert(0 <= newValue && newValue <= 1.0, "Danmaku display area must be between [0, 1].") }
        didSet { guard oldValue != displayArea else { return }; recalculateTracks() }
    }

    /// 是否允许弹幕重叠（true：可重叠；false：遵守防追尾逻辑）
    public var isOverlap = false {
        didSet {
            floatingTracks.forEach { $0.isOverlap = isOverlap }
            topTracks.forEach { $0.isOverlap = isOverlap }
            bottomTracks.forEach { $0.isOverlap = isOverlap }
        }
    }

    /// 播放速度（影响所有轨道的动画速度）
    public var playingSpeed: Float = 1.0 {
        didSet {
            floatingTracks.forEach { $0.playingSpeed = playingSpeed }
            topTracks.forEach { $0.playingSpeed = playingSpeed }
            bottomTracks.forEach { $0.playingSpeed = playingSpeed }
        }
    }

    /// 调试：是否显示左边界线并输出关键信息
    public var debugEnabled: Bool = false { didSet { updateDebugOverlay() } }
    /// 调试：额外的移除偏移（像素）。用于验证“是否完全离屏后再移除”
    public var debugRemovalOffset: CGFloat = 0
    private var debugLeftEdgeLayer: CALayer?

    /// 调试：是否逐帧追踪漂浮弹幕的 right 越界时刻
    public var debugTrackFloating: Bool = false

    private func updateDebugOverlay() {
        if debugEnabled {
            if debugLeftEdgeLayer == nil {
                let l = CALayer()
                l.backgroundColor = NSColor.red.withAlphaComponent(0.6).cgColor
                l.frame = CGRect(x: 0, y: 0, width: 2, height: bounds.height)
                layer?.addSublayer(l)
                debugLeftEdgeLayer = l
            }
            debugLeftEdgeLayer?.isHidden = false
            debugLeftEdgeLayer?.frame.size.height = bounds.height
        } else {
            debugLeftEdgeLayer?.isHidden = true
        }
    }

    /// 当前播放状态
    public private(set) var status: DanmakuStatus = .stop

    private var floatingTracks: [FloatingDanmakuTrack] = []
    private var topTracks: [TopDanmakuTrack] = []
    private var bottomTracks: [BottomDanmakuTrack] = []

    private var cellPool: [String: [DanmakuCell]] = [:]

    public override var isFlipped: Bool { true }

    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    public required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    deinit {
        stop()
    }

    private func setupView() {
        wantsLayer = true
        recalculateTracks()
    }

    public override func hitTest(_ point: NSPoint) -> NSView? {
        guard !isHidden, alphaValue > 0 else { return nil }
        guard self.bounds.contains(point) else { return nil }
        for sub in subviews.reversed() {
            var local = self.convert(point, to: sub)
            if let presentation = sub.layer?.presentation() {
                local = self.layer?.convert(point, to: presentation) ?? local
            }
            if let found = sub.hitTest(local) { return found }
        }
        return self
    }

    public override func layout() {
        super.layout()
        recalculateTracks()
    }

    /// Update closure like iOS: temporarily pause, mutate, then resume
    public func update(_ closure: () -> Void) {
        pause()
        closure()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            self.play()
        }
    }

    /// Start playing danmaku
    public func play() {
        guard status != .play else { return }
        status = .play
        floatingTracks.forEach { $0.play() }
        topTracks.forEach { $0.play() }
        bottomTracks.forEach { $0.play() }
    }

    /// Pause danmaku
    public func pause() {
        guard status == .play else { return }
        status = .pause
        floatingTracks.forEach { $0.pause() }
        topTracks.forEach { $0.pause() }
        bottomTracks.forEach { $0.pause() }
    }

    /// Stop danmaku and clean resources
    public func stop() {
        status = .stop
        floatingTracks.forEach { $0.stop() }
        topTracks.forEach { $0.stop() }
        bottomTracks.forEach { $0.stop() }
        cellPool.removeAll()
    }

    /// Shoot a danmaku
    public func shoot(danmaku: DanmakuCellModel) {
        guard status == .play else { return }
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return }
            guard !floatingTracks.isEmpty else { return }
        case .top:
            guard enableTopDanmaku else { return }
            guard !topTracks.isEmpty else { return }
        case .bottom:
            guard enableBottomDanmaku else { return }
            guard !bottomTracks.isEmpty else { return }
        }

        guard let cell = obtainCell(with: danmaku) else { return }

        let shootTrack: DanmakuTrack
        if isOverlap {
            shootTrack = findLeastNumberDanmakuTrack(for: danmaku)
        } else {
            guard let t = findSuitableTrack(for: danmaku) else {
                delegate?.danmakuView(self, noSpaceShoot: danmaku)
                if enableCellReusable {
                    appendCellToPool(cell)
                }
                return
            }
            shootTrack = t
        }

        if cell.superview == nil {
            addSubview(cell)
        }

        delegate?.danmakuView(self, willDisplay: cell)
        cell.layer?.setNeedsDisplay()
        shootTrack.shoot(danmaku: cell)
    }



    /// Clean all displayed danmaku
    public func clean() {
        floatingTracks.forEach { $0.clean() }
        bottomTracks.forEach { $0.clean() }
        topTracks.forEach { $0.clean() }
    }

    /// Check if can shoot danmaku (parity with iOS API)
    public func canShoot(danmaku: DanmakuCellModel) -> Bool {
        guard status == .play else { return false }
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return false }
            return floatingTracks.first { $0.canShoot(danmaku: danmaku) } != nil
        case .top:
            guard enableTopDanmaku else { return false }
            return topTracks.first { $0.canShoot(danmaku: danmaku) } != nil
        case .bottom:
            guard enableBottomDanmaku else { return false }
            return bottomTracks.first { $0.canShoot(danmaku: danmaku) } != nil
        }
    }

    /// Recalculate tracks based on current frame
    public func recalculateTracks() {
        guard bounds.height > 0 else { return }

        // Match iOS: use displayArea and center available tracks vertically
        let viewHeight = bounds.height * displayArea
        let maxCount = Int(floorf(Float((viewHeight - paddingTop - paddingBottom) / trackHeight)))
        let offsetY = max(0, (viewHeight - CGFloat(maxCount) * trackHeight) / 2.0)

        // Update floating/top/bottom track counts
        updateTracks(&floatingTracks, count: max(0, maxCount), type: FloatingDanmakuTrack.self)
        updateTracks(&topTracks, count: max(0, maxCount), type: TopDanmakuTrack.self)
        updateTracks(&bottomTracks, count: max(0, maxCount), type: BottomDanmakuTrack.self)

        // Floating positions (top-down)
        for (index, track) in floatingTracks.enumerated() {
            track.positionY = paddingTop + offsetY + trackHeight * CGFloat(index) + trackHeight / 2
            track.index = UInt(index)
        }

        // Top positions (top-down)
        for (index, track) in topTracks.enumerated() {
            track.positionY = paddingTop + offsetY + trackHeight * CGFloat(index) + trackHeight / 2
            track.index = UInt(index)
        }

        // Bottom positions (bottom-up)
        for (index, track) in bottomTracks.enumerated() {
            let revIndex = bottomTracks.count - index - 1
            track.positionY = bounds.height - (paddingBottom + offsetY + trackHeight * CGFloat(revIndex) + trackHeight / 2)
            track.index = UInt(index)
        }
    }

    private func updateTracks<T: DanmakuTrack>(_ tracks: inout [T], count: Int, type: T.Type) {
        let currentCount = tracks.count

        if currentCount < count {
            // Add new tracks
            for _ in currentCount..<count {
                var track = type.init(view: self)
                track.isOverlap = isOverlap
                track.playingSpeed = playingSpeed
                track.stopClosure = { [weak self] cell in
                    self?.handleDanmakuStop(cell)
                }
                tracks.append(track)
            }
        } else if currentCount > count {
            // Remove excess tracks
            let tracksToRemove = tracks.suffix(currentCount - count)
            tracksToRemove.forEach { $0.clean() }
            tracks.removeLast(currentCount - count)
        }
    }

    private func handleDanmakuStop(_ cell: DanmakuCell) {
        delegate?.danmakuView(self, didEndDisplaying: cell)
        cell.removeFromSuperview()
        if enableCellReusable {
            appendCellToPool(cell)
        }
    }

    private func findSuitableTrack(for danmaku: DanmakuCellModel) -> DanmakuTrack? {
        switch danmaku.type {
        case .floating:
            return floatingTracks.first { $0.canShoot(danmaku: danmaku) }
        case .top:
            return topTracks.first { $0.canShoot(danmaku: danmaku) }
        case .bottom:
            // choose from bottom-most available track like iOS
            return bottomTracks.last { $0.canShoot(danmaku: danmaku) }
        }
    }

    private func findLeastNumberDanmakuTrack(for danmaku: DanmakuCellModel) -> DanmakuTrack {
        switch danmaku.type {
        case .floating:
            return floatingTracks.min { $0.danmakuCount < $1.danmakuCount } ?? floatingTracks[0]
        case .top:
            return topTracks.min { $0.danmakuCount < $1.danmakuCount } ?? topTracks[0]
        case .bottom:
            return bottomTracks.min { $0.danmakuCount < $1.danmakuCount } ?? bottomTracks[0]
        }
    }

    private func obtainCell(with danmaku: DanmakuCellModel) -> DanmakuCell? {
        var cell: DanmakuCell?
        if enableCellReusable {
            cell = cellFromPool(danmaku)
        }

        let frame = CGRect(x: bounds.width, y: 0, width: danmaku.size.width, height: danmaku.size.height)
        if cell == nil {
            guard let cls = NSClassFromString(NSStringFromClass(danmaku.cellClass)) as? DanmakuCell.Type else {
                assert(false, "Launched Danmaku must inherit from DanmakuCell!")
                return nil
            }
            cell = cls.init(frame: frame)
            cell?.model = danmaku
            let click = NSClickGestureRecognizer(target: self, action: #selector(danmakuDidClick(_:)))
            cell?.addGestureRecognizer(click)
        } else {
            cell?.frame = frame
            cell?.model = danmaku
            delegate?.danmakuView(self, dequeueReusable: cell!)
        }
        return cell
    }

    @objc private func danmakuDidClick(_ gesture: NSClickGestureRecognizer) {
        guard let cell = gesture.view as? DanmakuCell else { return }
        delegate?.danmakuView(self, didClicked: cell)
        delegate?.danmakuView(self, didTapped: cell)
    }

    private func cellFromPool(_ danmaku: DanmakuCellModel) -> DanmakuCell? {
        let className = NSStringFromClass(danmaku.cellClass)
        guard let cells = cellPool[className], !cells.isEmpty else { return nil }
        let cell = cells.last!
        cellPool[className]?.removeLast()
        return cell
    }

    private func appendCellToPool(_ cell: DanmakuCell) {
        let className = NSStringFromClass(type(of: cell))
        if cellPool[className] == nil {
            cellPool[className] = []
        }
        cellPool[className]?.append(cell)
    }

    /// Sync display danmaku at specific progress
    public func sync(danmaku: DanmakuCellModel, at progress: Float) {
        guard status != .stop else { return }
        assert(progress <= 1.0, "Cannot sync danmaku at progress \(progress).")
        switch danmaku.type {
        case .floating:
            guard enableFloatingDanmaku else { return }
            guard !floatingTracks.isEmpty else { return }
        case .top:
            guard enableTopDanmaku else { return }
            guard !topTracks.isEmpty else { return }
        case .bottom:
            guard enableBottomDanmaku else { return }
            guard !bottomTracks.isEmpty else { return }
        }
        guard let cell = obtainCell(with: danmaku) else { return }

        let syncTrack: DanmakuTrack
        if isOverlap {
            syncTrack = findLeastNumberDanmakuTrack(for: danmaku)
        } else {
            guard let t = findSuitableSyncTrack(for: danmaku, at: progress) else {
                delegate?.danmakuView(self, noSpaceSync: danmaku)
                return
            }
            syncTrack = t
        }

        if cell.superview == nil {
            addSubview(cell)
        }

        delegate?.danmakuView(self, willDisplay: cell)
        cell.layer?.setNeedsDisplay()
        if status == .play {
            syncTrack.syncAndPlay(cell, at: progress)
        } else {
            syncTrack.sync(cell, at: progress)
        }
    }

    /// Play a specific danmaku by model, returns true if found and resumed
    @discardableResult
    public func play(_ danmaku: DanmakuCellModel) -> Bool {
        if let t = floatingTracks.first(where: { $0.play(danmaku) }) { _ = t; return true }
        if let t = topTracks.first(where: { $0.play(danmaku) }) { _ = t; return true }
        if let t = bottomTracks.first(where: { $0.play(danmaku) }) { _ = t; return true }
        return false
    }

    /// Pause a specific danmaku by model, returns true if found and paused
    @discardableResult
    public func pause(_ danmaku: DanmakuCellModel) -> Bool {
        if let t = floatingTracks.first(where: { $0.pause(danmaku) }) { _ = t; return true }
        if let t = topTracks.first(where: { $0.pause(danmaku) }) { _ = t; return true }
        if let t = bottomTracks.first(where: { $0.pause(danmaku) }) { _ = t; return true }
        return false
    }

    private func findSuitableSyncTrack(for danmaku: DanmakuCellModel, at progress: Float) -> DanmakuTrack? {
        switch danmaku.type {
        case .floating:
            return floatingTracks.first { $0.canSync(danmaku, at: progress) }
        case .top:
            return topTracks.first { $0.canSync(danmaku, at: progress) }
        case .bottom:
            return bottomTracks.last { $0.canSync(danmaku, at: progress) }
        }
    }

}
